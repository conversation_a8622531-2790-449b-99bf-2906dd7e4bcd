@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.app {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  min-height: 100vh;
}

.app.rtl {
  direction: rtl;
  font-family: 'Cairo', 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
}

.app.ltr {
  direction: ltr;
}

.app-header {
  text-align: center;
  margin-bottom: 3rem;
  position: relative;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 2.5rem 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.language-switcher {
  position: absolute;
  top: 1rem;
  right: 1rem;
  display: flex;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  padding: 0.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.app.rtl .language-switcher {
  right: auto;
  left: 1rem;
}

.lang-btn {
  padding: 0.6rem 1.2rem;
  border: none;
  background: transparent;
  color: #666;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  font-family: inherit;
}

.lang-btn:hover {
  background: rgba(37, 211, 102, 0.1);
  color: #25D366;
  transform: translateY(-1px);
}

.lang-btn.active {
  background: #25D366;
  color: white;
  box-shadow: 0 2px 8px rgba(37, 211, 102, 0.3);
}

.app-header h1 {
  color: #25D366;
  font-size: 3rem;
  margin-bottom: 0.5rem;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  background: linear-gradient(135deg, #25D366, #128C7E);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.app-header p {
  color: #555;
  font-size: 1.2rem;
  font-weight: 400;
  opacity: 0.8;
  margin-bottom: 1.5rem;
}

.feature-badges {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: 1.5rem;
}

.badge {
  background: rgba(37, 211, 102, 0.1);
  color: #25D366;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  border: 1px solid rgba(37, 211, 102, 0.2);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.badge:hover {
  background: rgba(37, 211, 102, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(37, 211, 102, 0.3);
}

.main-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.upload-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 3rem 2rem;
  border-radius: 20px;
  border: 2px dashed rgba(37, 211, 102, 0.3);
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

.upload-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(37, 211, 102, 0.05), rgba(18, 140, 126, 0.05));
  pointer-events: none;
}

.upload-section:hover {
  border-color: rgba(37, 211, 102, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.upload-section.drag-over {
  border-color: rgba(37, 211, 102, 0.8);
  background: rgba(37, 211, 102, 0.1);
  transform: scale(1.02);
}

.drop-zone {
  position: relative;
  z-index: 1;
}

.drop-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.7;
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.or-text {
  color: #666;
  font-size: 1rem;
  margin: 1.5rem 0;
  font-weight: 500;
  opacity: 0.8;
}

.upload-section h2 {
  margin-bottom: 2rem;
  color: #333;
  font-weight: 600;
  font-size: 1.5rem;
  position: relative;
  z-index: 1;
}

.upload-buttons {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  flex-wrap: wrap;
  position: relative;
  z-index: 1;
}

.upload-option {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.upload-btn {
  background: linear-gradient(135deg, #25D366, #128C7E);
  color: white;
  padding: 1.2rem 2.5rem;
  border-radius: 15px;
  cursor: pointer;
  font-size: 1.1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  border: none;
  display: inline-block;
  text-decoration: none;
  box-shadow: 0 4px 15px rgba(37, 211, 102, 0.3);
  position: relative;
  overflow: hidden;
}

.upload-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.upload-btn:hover::before {
  left: 100%;
}

.upload-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(37, 211, 102, 0.4);
}

.file-info {
  margin-top: 1.5rem;
  padding: 1.5rem;
  background: rgba(232, 245, 232, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 1rem;
  border: 1px solid rgba(37, 211, 102, 0.2);
  position: relative;
  z-index: 1;
}

.file-info p {
  margin: 0;
  color: #2d5a3d;
  font-weight: 500;
}

.reset-btn {
  background: linear-gradient(135deg, #dc3545, #c82333);
  color: white;
  border: none;
  padding: 0.7rem 1.5rem;
  border-radius: 10px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
}

.reset-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(220, 53, 69, 0.4);
}

.scanning {
  text-align: center;
  padding: 3rem 2rem;
  background: rgba(255, 243, 205, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  border: 1px solid rgba(255, 234, 167, 0.5);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.scanning p {
  color: #856404;
  font-weight: 500;
  font-size: 1.1rem;
  margin: 0;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(37, 211, 102, 0.2);
  border-top: 4px solid #25D366;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1.5rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.results-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 2.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  margin-top: 2rem;
}

.results-section h2 {
  color: #333;
  margin-bottom: 2rem;
  font-size: 2rem;
  font-weight: 700;
  text-align: center;
  background: linear-gradient(135deg, #25D366, #128C7E);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.results-summary {
  margin-bottom: 2.5rem;
}

.summary-card {
  background: linear-gradient(135deg, rgba(37, 211, 102, 0.1), rgba(18, 140, 126, 0.1));
  padding: 2rem;
  border-radius: 15px;
  border: 1px solid rgba(37, 211, 102, 0.2);
  box-shadow: 0 4px 20px rgba(37, 211, 102, 0.1);
  position: relative;
  overflow: hidden;
}

.summary-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(135deg, #25D366, #128C7E);
}

.summary-card h3 {
  margin-bottom: 1.5rem;
  color: #333;
  font-weight: 600;
  font-size: 1.3rem;
}

.summary-card p {
  margin: 0.8rem 0;
  color: #555;
  font-weight: 500;
}

.backup-files, .suspicious-files {
  margin-bottom: 2.5rem;
}

.backup-files h3 {
  color: #28a745;
  margin-bottom: 1.5rem;
  font-size: 1.4rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.suspicious-files h3 {
  color: #f39c12;
  margin-bottom: 1.5rem;
  font-size: 1.4rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.file-list {
  display: grid;
  gap: 1.5rem;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
}

.file-item {
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
  padding: 2rem;
  border-radius: 15px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.file-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  pointer-events: none;
}

.app.rtl .file-item {
  flex-direction: row-reverse;
}

.file-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.file-item.confirmed {
  background: linear-gradient(135deg, rgba(212, 237, 218, 0.9), rgba(195, 230, 203, 0.9));
  border-color: rgba(40, 167, 69, 0.3);
  box-shadow: 0 4px 20px rgba(40, 167, 69, 0.2);
}

.file-item.suspicious {
  background: linear-gradient(135deg, rgba(255, 243, 205, 0.9), rgba(255, 234, 167, 0.9));
  border-color: rgba(243, 156, 18, 0.3);
  box-shadow: 0 4px 20px rgba(243, 156, 18, 0.2);
}

.file-icon {
  font-size: 2.5rem;
  flex-shrink: 0;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  padding: 0.8rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
}

.file-details {
  flex: 1;
  position: relative;
  z-index: 1;
}

.app.rtl .file-details {
  text-align: right;
}

.file-details h4 {
  margin: 0 0 0.8rem 0;
  color: #333;
  font-size: 1.2rem;
  font-weight: 600;
  line-height: 1.3;
}

.file-path {
  color: #666;
  font-size: 0.9rem;
  margin: 0 0 1rem 0;
  word-break: break-all;
  background: rgba(255, 255, 255, 0.6);
  padding: 0.5rem 0.8rem;
  border-radius: 8px;
  font-family: 'Courier New', monospace;
}

.file-meta {
  display: flex;
  gap: 1.2rem;
  flex-wrap: wrap;
  font-size: 0.9rem;
  color: #555;
  font-weight: 500;
}

.app.rtl .file-meta {
  flex-direction: row-reverse;
  text-align: right;
}

.file-meta span {
  background: rgba(255, 255, 255, 0.7);
  padding: 0.4rem 0.8rem;
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.confidence {
  padding: 0.4rem 1rem;
  border-radius: 20px;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.confidence.high {
  background: linear-gradient(135deg, #d4edda, #c3e6cb);
  color: #155724;
  border: 1px solid rgba(21, 87, 36, 0.2);
}

.confidence.medium {
  background: linear-gradient(135deg, #fff3cd, #ffeaa7);
  color: #856404;
  border: 1px solid rgba(133, 100, 4, 0.2);
}

.no-results {
  text-align: center;
  padding: 3rem 2rem;
  background: linear-gradient(135deg, rgba(248, 215, 218, 0.9), rgba(245, 198, 203, 0.9));
  backdrop-filter: blur(10px);
  border-radius: 20px;
  border: 1px solid rgba(114, 28, 36, 0.2);
  box-shadow: 0 8px 32px rgba(114, 28, 36, 0.1);
}

.no-results h3 {
  color: #721c24;
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
  font-weight: 600;
}

.no-results p {
  color: #721c24;
  font-size: 1.1rem;
  margin-bottom: 2rem;
  opacity: 0.8;
}

.tips {
  text-align: left;
  margin-top: 2rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 2rem;
  border-radius: 15px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.app.rtl .tips {
  text-align: right;
}

.tips h4 {
  color: #333;
  margin-bottom: 1.5rem;
  font-weight: 600;
  font-size: 1.2rem;
}

.tips ul {
  color: #555;
  line-height: 1.8;
  font-weight: 400;
}

.tips li {
  margin-bottom: 0.8rem;
  padding-left: 0.5rem;
}

.app.rtl .tips li {
  padding-left: 0;
  padding-right: 0.5rem;
}

.info-section {
  background: rgba(233, 236, 239, 0.8);
  backdrop-filter: blur(10px);
  padding: 3rem 2rem;
  border-radius: 20px;
  margin-top: 3rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.info-section h3 {
  color: #333;
  margin-bottom: 2.5rem;
  text-align: center;
  font-size: 2rem;
  font-weight: 700;
  background: linear-gradient(135deg, #25D366, #128C7E);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.info-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 2.5rem;
  border-radius: 20px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.info-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(135deg, #25D366, #128C7E);
}

.info-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.info-card h4 {
  color: #25D366;
  margin-bottom: 1.5rem;
  font-size: 1.3rem;
  font-weight: 600;
}

.info-card ul {
  color: #555;
  line-height: 1.8;
  font-weight: 400;
}

.app.rtl .info-card ul {
  text-align: right;
  padding-right: 1.5rem;
  padding-left: 0;
}

.info-card li {
  margin-bottom: 0.8rem;
  padding: 0.3rem 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.info-card li:last-child {
  border-bottom: none;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.app-header {
  animation: fadeInUp 0.8s ease-out;
}

.upload-section {
  animation: fadeInUp 0.8s ease-out 0.2s both;
}

.results-section {
  animation: fadeInUp 0.8s ease-out 0.4s both;
}

.info-section {
  animation: fadeInUp 0.8s ease-out 0.6s both;
}

.file-item {
  animation: slideInRight 0.6s ease-out;
}

.app.rtl .file-item {
  animation: slideInLeft 0.6s ease-out;
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {
  .app {
    padding: 1rem;
  }

  .app-header {
    padding: 2rem 1.5rem;
  }

  .app-header h1 {
    font-size: 2.2rem;
  }

  .language-switcher {
    position: relative;
    top: auto;
    right: auto;
    left: auto;
    justify-content: center;
    margin-bottom: 1.5rem;
  }

  .upload-section {
    padding: 2rem 1.5rem;
  }

  .upload-buttons {
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }

  .upload-btn {
    width: 100%;
    max-width: 280px;
  }

  .file-list {
    grid-template-columns: 1fr;
  }

  .file-item {
    padding: 1.5rem;
  }

  .file-meta {
    flex-direction: column;
    gap: 0.8rem;
  }

  .info-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .info-card {
    padding: 2rem 1.5rem;
  }

  .summary-card {
    padding: 1.5rem;
  }
}

@media (max-width: 480px) {
  .app {
    padding: 0.5rem;
  }

  .app-header {
    padding: 1.5rem 1rem;
  }

  .app-header h1 {
    font-size: 1.8rem;
  }

  .upload-section {
    padding: 1.5rem 1rem;
  }

  .file-item {
    padding: 1rem;
    gap: 1rem;
  }

  .file-icon {
    font-size: 2rem;
    padding: 0.6rem;
  }

  .info-card {
    padding: 1.5rem 1rem;
  }
}

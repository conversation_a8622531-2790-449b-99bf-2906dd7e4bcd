import { useState, useRef } from 'react'
import './App.css'

function App() {
  const [backupFiles, setBackupFiles] = useState([])
  const [isScanning, setIsScanning] = useState(false)
  const [scanResults, setScanResults] = useState(null)
  const [language, setLanguage] = useState('en')
  const [isDragOver, setIsDragOver] = useState(false)
  const fileInputRef = useRef(null)

  // Translations
  const translations = {
    en: {
      title: "📱 WhatsApp Backup Checker",
      subtitle: "Drop files or folders here to automatically detect WhatsApp backups",
      detectFiles: "📁 Detect Files",
      detectFolder: "📂 Detect Folder",
      dropZoneText: "Drop files or folders here to detect WhatsApp backups",
      orText: "or",
      detectedFiles: "Detected {count} files",
      reset: "🔄 Reset",
      scanning: "Analyzing files for WhatsApp backups...",
      scanResults: "Scan Results",
      summary: "📊 Summary",
      totalScanned: "Total files scanned: {count}",
      backupsFound: "WhatsApp backups found: {count}",
      suspiciousFiles: "Suspicious files: {count}",
      scannedAt: "Scanned at: {time}",
      confirmedBackups: "✅ WhatsApp Backup Files Found",
      potentialFiles: "⚠️ Potentially Related Files",
      noBackupsFound: "❌ No WhatsApp Backup Files Found",
      noBackupsMessage: "No WhatsApp backup files were detected in the selected files/folders.",
      tipsTitle: "💡 Tips for finding WhatsApp backups:",
      tip1: "Check your phone's internal storage under WhatsApp/Databases/",
      tip2: "Look for files with extensions like .db, .crypt12, .crypt14, etc.",
      tip3: "Check cloud storage (Google Drive, iCloud) for WhatsApp backups",
      tip4: "Look for files named msgstore.db.crypt or similar",
      tip5: "For iOS: Check iTunes/Finder backups or iCloud backup files",
      tip6: "iOS backups may be in .plist or .mddata format",
      aboutTitle: "ℹ️ About WhatsApp Backups",
      commonTypes: "Common File Types",
      typicalLocations: "Typical Locations",
      androidLocation: "Android: /WhatsApp/Databases/",
      iosLocation: "iPhone: iTunes/iCloud backups",
      cloudLocation: "Cloud storage services",
      computerLocation: "Computer backup folders",
      iosBackupLocation: "iOS: ~/Library/Application Support/MobileSync/Backup/",
      highConfidence: "High Confidence",
      mediumConfidence: "Medium Confidence",
      size: "Size: {size}",
      modified: "Modified: {date}"
    },
    ar: {
      title: "📱 فاحص نسخ واتساب الاحتياطية",
      subtitle: "اسحب الملفات أو المجلدات هنا للكشف التلقائي عن نسخ واتساب الاحتياطية",
      detectFiles: "📁 كشف الملفات",
      detectFolder: "📂 كشف المجلد",
      dropZoneText: "اسحب الملفات أو المجلدات هنا لكشف نسخ واتساب الاحتياطية",
      orText: "أو",
      detectedFiles: "تم كشف {count} ملف",
      reset: "🔄 إعادة تعيين",
      scanning: "جاري تحليل الملفات للبحث عن نسخ واتساب الاحتياطية...",
      scanResults: "نتائج الفحص",
      summary: "📊 ملخص",
      totalScanned: "إجمالي الملفات المفحوصة: {count}",
      backupsFound: "نسخ واتساب الاحتياطية الموجودة: {count}",
      suspiciousFiles: "الملفات المشبوهة: {count}",
      scannedAt: "تم الفحص في: {time}",
      confirmedBackups: "✅ تم العثور على ملفات النسخ الاحتياطية لواتساب",
      potentialFiles: "⚠️ ملفات محتملة ذات صلة",
      noBackupsFound: "❌ لم يتم العثور على ملفات النسخ الاحتياطية لواتساب",
      noBackupsMessage: "لم يتم اكتشاف أي ملفات نسخ احتياطية لواتساب في الملفات/المجلدات المحددة.",
      tipsTitle: "💡 نصائح للعثور على النسخ الاحتياطية لواتساب:",
      tip1: "تحقق من التخزين الداخلي لهاتفك تحت WhatsApp/Databases/",
      tip2: "ابحث عن الملفات بامتدادات مثل .db، .crypt12، .crypt14، إلخ",
      tip3: "تحقق من التخزين السحابي (Google Drive، iCloud) للنسخ الاحتياطية لواتساب",
      tip4: "ابحث عن الملفات المسماة msgstore.db.crypt أو ما شابه",
      tip5: "لنظام iOS: تحقق من نسخ iTunes/Finder الاحتياطية أو ملفات iCloud الاحتياطية",
      tip6: "قد تكون النسخ الاحتياطية لنظام iOS بتنسيق .plist أو .mddata",
      aboutTitle: "ℹ️ حول النسخ الاحتياطية لواتساب",
      commonTypes: "أنواع الملفات الشائعة",
      typicalLocations: "المواقع النموذجية",
      androidLocation: "أندرويد: /WhatsApp/Databases/",
      iosLocation: "آيفون: نسخ iTunes/iCloud الاحتياطية",
      cloudLocation: "خدمات التخزين السحابي",
      computerLocation: "مجلدات النسخ الاحتياطية للكمبيوتر",
      iosBackupLocation: "iOS: ~/Library/Application Support/MobileSync/Backup/",
      highConfidence: "ثقة عالية",
      mediumConfidence: "ثقة متوسطة",
      size: "الحجم: {size}",
      modified: "تم التعديل: {date}"
    }
  }

  // Enhanced WhatsApp backup file patterns including iOS
  const whatsappPatterns = [
    // Android patterns
    /WhatsApp.*\.db$/i,
    /msgstore.*\.db$/i,
    /wa\.db$/i,
    /WhatsApp.*\.crypt\d+$/i,
    /msgstore.*\.crypt\d+$/i,
    /WhatsApp.*backup.*\.zip$/i,
    /WhatsApp.*\.tar$/i,
    /WhatsApp.*\.backup$/i,
    /databases.*WhatsApp/i,
    /\.nomedia$/i,

    // iOS patterns
    /ChatStorage\.sqlite$/i,
    /.*WhatsApp.*\.sqlite$/i,
    /.*WhatsApp.*\.plist$/i,
    /.*WhatsApp.*\.mddata$/i,
    /.*WhatsApp.*\.mdinfo$/i,
    /Manifest\.plist$/i,
    /Info\.plist$/i,
    /.*net\.whatsapp\.WhatsApp.*/i,
    /.*7c7fba66680ef796b916b067077cc246adacf01d$/i, // WhatsApp iOS bundle identifier hash
    /.*AppDomain-net\.whatsapp\.WhatsApp/i,
    /.*Documents.*WhatsApp/i,
    /.*Library.*WhatsApp/i
  ]

  const t = (key, params = {}) => {
    let text = translations[language][key] || key
    Object.keys(params).forEach(param => {
      text = text.replace(`{${param}}`, params[param])
    })
    return text
  }

  const handleFileSelect = (event) => {
    const files = Array.from(event.target.files)
    setBackupFiles(files)
    scanForWhatsAppBackups(files)
  }

  const handleFolderSelect = (event) => {
    const files = Array.from(event.target.files)
    setBackupFiles(files)
    scanForWhatsAppBackups(files)
  }

  const handleDrop = (event) => {
    event.preventDefault()
    setIsDragOver(false)
    const files = Array.from(event.dataTransfer.files)
    setBackupFiles(files)
    scanForWhatsAppBackups(files)
  }

  const handleDragOver = (event) => {
    event.preventDefault()
  }

  const handleDragEnter = (event) => {
    event.preventDefault()
    setIsDragOver(true)
  }

  const handleDragLeave = (event) => {
    event.preventDefault()
    setIsDragOver(false)
  }

  const scanForWhatsAppBackups = (files) => {
    setIsScanning(true)

    const whatsappFiles = []
    const suspiciousFiles = []

    files.forEach(file => {
      const fileName = file.name
      const filePath = file.webkitRelativePath || fileName

      // Check for exact WhatsApp backup patterns
      const isWhatsAppFile = whatsappPatterns.some(pattern =>
        pattern.test(fileName) || pattern.test(filePath)
      )

      if (isWhatsAppFile) {
        whatsappFiles.push({
          name: fileName,
          path: filePath,
          size: file.size,
          type: file.type,
          lastModified: new Date(file.lastModified),
          confidence: 'High'
        })
      } else if (
        fileName.toLowerCase().includes('whatsapp') ||
        fileName.toLowerCase().includes('backup') ||
        filePath.toLowerCase().includes('whatsapp') ||
        fileName.endsWith('.db') ||
        fileName.includes('crypt') ||
        fileName.endsWith('.sqlite') ||
        fileName.endsWith('.plist') ||
        fileName.includes('net.whatsapp') ||
        filePath.toLowerCase().includes('mobilesync') ||
        filePath.toLowerCase().includes('itunes')
      ) {
        suspiciousFiles.push({
          name: fileName,
          path: filePath,
          size: file.size,
          type: file.type,
          lastModified: new Date(file.lastModified),
          confidence: 'Medium'
        })
      }
    })

    setScanResults({
      whatsappFiles,
      suspiciousFiles,
      totalFiles: files.length,
      scannedAt: new Date()
    })

    setIsScanning(false)
  }

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const resetScan = () => {
    setBackupFiles([])
    setScanResults(null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  return (
    <div className={`app ${language === 'ar' ? 'rtl' : 'ltr'}`}>
      <header className="app-header">
        <div className="language-switcher">
          <button
            onClick={() => setLanguage('en')}
            className={`lang-btn ${language === 'en' ? 'active' : ''}`}
          >
            EN
          </button>
          <button
            onClick={() => setLanguage('ar')}
            className={`lang-btn ${language === 'ar' ? 'active' : ''}`}
          >
            العربية
          </button>
        </div>
        <h1>{t('title')}</h1>
        <p>{t('subtitle')}</p>
        <div className="feature-badges">
          <span className="badge">🤖 AI-Powered Detection</span>
          <span className="badge">🔒 100% Private</span>
          <span className="badge">📱 iOS & Android</span>
        </div>
      </header>

      <main className="main-content">
        <div
          className={`upload-section ${isDragOver ? 'drag-over' : ''}`}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onDragEnter={handleDragEnter}
          onDragLeave={handleDragLeave}
        >
          <div className="drop-zone">
            <div className="drop-icon">📁</div>
            <h2>{t('dropZoneText')}</h2>
            <p className="or-text">{t('orText')}</p>

            <div className="upload-buttons">
              <div className="upload-option">
                <label htmlFor="file-input" className="upload-btn">
                  {t('detectFiles')}
                </label>
                <input
                  id="file-input"
                  ref={fileInputRef}
                  type="file"
                  multiple
                  onChange={handleFileSelect}
                  style={{ display: 'none' }}
                />
              </div>

              <div className="upload-option">
                <label htmlFor="folder-input" className="upload-btn">
                  {t('detectFolder')}
                </label>
                <input
                  id="folder-input"
                  type="file"
                  webkitdirectory=""
                  onChange={handleFolderSelect}
                  style={{ display: 'none' }}
                />
              </div>
            </div>
          </div>

          {backupFiles.length > 0 && (
            <div className="file-info">
              <p>{t('detectedFiles', { count: backupFiles.length })}</p>
              <button onClick={resetScan} className="reset-btn">
                {t('reset')}
              </button>
            </div>
          )}
        </div>

        {isScanning && (
          <div className="scanning">
            <div className="spinner"></div>
            <p>{t('scanning')}</p>
          </div>
        )}

        {scanResults && (
          <div className="results-section">
            <h2>{t('scanResults')}</h2>
            <div className="results-summary">
              <div className="summary-card">
                <h3>{t('summary')}</h3>
                <p>{t('totalScanned', { count: scanResults.totalFiles })}</p>
                <p>{t('backupsFound', { count: scanResults.whatsappFiles.length })}</p>
                <p>{t('suspiciousFiles', { count: scanResults.suspiciousFiles.length })}</p>
                <p>{t('scannedAt', { time: scanResults.scannedAt.toLocaleString('en-US') })}</p>
              </div>
            </div>

            {scanResults.whatsappFiles.length > 0 && (
              <div className="backup-files">
                <h3>{t('confirmedBackups')}</h3>
                <div className="file-list">
                  {scanResults.whatsappFiles.map((file, index) => (
                    <div key={index} className="file-item confirmed">
                      <div className="file-icon">📱</div>
                      <div className="file-details">
                        <h4>{file.name}</h4>
                        <p className="file-path">{file.path}</p>
                        <div className="file-meta">
                          <span>{t('size', { size: formatFileSize(file.size) })}</span>
                          <span>{t('modified', { date: file.lastModified.toLocaleDateString('en-US') })}</span>
                          <span className="confidence high">{t('highConfidence')}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {scanResults.suspiciousFiles.length > 0 && (
              <div className="suspicious-files">
                <h3>{t('potentialFiles')}</h3>
                <div className="file-list">
                  {scanResults.suspiciousFiles.map((file, index) => (
                    <div key={index} className="file-item suspicious">
                      <div className="file-icon">❓</div>
                      <div className="file-details">
                        <h4>{file.name}</h4>
                        <p className="file-path">{file.path}</p>
                        <div className="file-meta">
                          <span>{t('size', { size: formatFileSize(file.size) })}</span>
                          <span>{t('modified', { date: file.lastModified.toLocaleDateString('en-US') })}</span>
                          <span className="confidence medium">{t('mediumConfidence')}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {scanResults.whatsappFiles.length === 0 && scanResults.suspiciousFiles.length === 0 && (
              <div className="no-results">
                <h3>{t('noBackupsFound')}</h3>
                <p>{t('noBackupsMessage')}</p>
                <div className="tips">
                  <h4>{t('tipsTitle')}</h4>
                  <ul>
                    <li>{t('tip1')}</li>
                    <li>{t('tip2')}</li>
                    <li>{t('tip3')}</li>
                    <li>{t('tip4')}</li>
                    <li>{t('tip5')}</li>
                    <li>{t('tip6')}</li>
                  </ul>
                </div>
              </div>
            )}
          </div>
        )}

        <div className="info-section">
          <h3>{t('aboutTitle')}</h3>
          <div className="info-grid">
            <div className="info-card">
              <h4>{t('commonTypes')}</h4>
              <ul>
                <li>.db files (database files)</li>
                <li>.crypt12/.crypt14 (encrypted backups)</li>
                <li>.sqlite files (iOS database files)</li>
                <li>.plist files (iOS property lists)</li>
                <li>.zip archives containing backups</li>
                <li>msgstore.db files</li>
                <li>.mddata/.mdinfo files (iOS backup metadata)</li>
              </ul>
            </div>
            <div className="info-card">
              <h4>{t('typicalLocations')}</h4>
              <ul>
                <li>{t('androidLocation')}</li>
                <li>{t('iosLocation')}</li>
                <li>{t('iosBackupLocation')}</li>
                <li>{t('cloudLocation')}</li>
                <li>{t('computerLocation')}</li>
              </ul>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}

export default App
